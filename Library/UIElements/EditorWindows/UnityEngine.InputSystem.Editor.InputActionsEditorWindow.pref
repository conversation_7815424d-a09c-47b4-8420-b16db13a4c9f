%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 61
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12386, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: UnityEditor.dll::UnityEditor.UIElements.SerializableJsonDictionary
  m_Keys:
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__UnityEngine.UIElements.TwoPaneSplitView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__UnityEngine.UIElements.TwoPaneSplitView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_052faaac586de48259a63d0c4782560b__UnityEngine.UIElements.TreeView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_052faaac586de48259a63d0c4782560b__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_052faaac586de48259a63d0c4782560b__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_ff06d1af3c68d4902a442b02ec3c0005__UnityEngine.UIElements.TreeView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_ff06d1af3c68d4902a442b02ec3c0005__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_ff06d1af3c68d4902a442b02ec3c0005__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_0952c9d6bb6994e3bb4e108691815318__UnityEngine.UIElements.TreeView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_0952c9d6bb6994e3bb4e108691815318__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_0952c9d6bb6994e3bb4e108691815318__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_88302a77a447249db98969bfd6c20478__UnityEngine.UIElements.TreeView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_88302a77a447249db98969bfd6c20478__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_88302a77a447249db98969bfd6c20478__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_998842a8650da49628965a63c803f47e__UnityEngine.UIElements.TreeView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_998842a8650da49628965a63c803f47e__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_998842a8650da49628965a63c803f47e__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_3ccb2eba9285441029b1a58096e15a03__UnityEngine.UIElements.TreeView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_3ccb2eba9285441029b1a58096e15a03__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_3ccb2eba9285441029b1a58096e15a03__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_aa7d4a4d5e05d4a8294dc27b92e64992__UnityEngine.UIElements.TreeView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_aa7d4a4d5e05d4a8294dc27b92e64992__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_aa7d4a4d5e05d4a8294dc27b92e64992__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_1451f8e107b864bab9a530b128e6067a__UnityEngine.UIElements.TreeView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_1451f8e107b864bab9a530b128e6067a__unity-vertical-collection-scroll-view__UnityEngine.UIElements.ScrollView
  - __PanelContainer__rootVisualContainer__actions-editor-splitter-1__actions-editor-splitter-2__InputActionTreeView_1451f8e107b864bab9a530b128e6067a__unity-vertical-collection-scroll-view__Slider__UnityEngine.UIElements.Scroller+ScrollerSlider
  m_Values:
  - '{"m_FixedPaneDimension":205.0}'
  - '{"m_FixedPaneDimension":320.0}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{}'
  - '{"m_ShowAlternatingRowBackgrounds":0,"serializedVirtualizationData":{"scrollOffset":{"x":0.0,"y":0.0},"firstVisibleIndex":0,"contentPadding":0.0,"contentHeight":0.0,"anchoredItemIndex":0,"anchorOffset":0.0},"m_SelectedIds":[1841716428],"m_ExpandedItemIds":[-1186866560,2074457524]}'
  - '{"m_ScrollOffset":{"x":0.0,"y":0.0}}'
  - '{"m_Value":0.0,"m_LowValue":0.0,"m_HighValue":0.0}'
  - '{}'
  - '{}'
  - '{}'
