using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Interactions;

public class IPS07InputInteractions : MonoBehaviour
{
  // 用于使用Input System
  private PlayerInputs myInput;
  // 用于保存初始颜色，用于恢复
  private Color initialColor;
  // 用于保存鼠标点击的物体
  [SerializeField] private Transform target;
  // 用于添加相机组件
  public Camera myCamera;
  // 忽略地面层
  public LayerMask ignoreLayer;

  void Awake()
  {
    myInput ??= new PlayerInputs();
    if (myCamera == null)
    {
      myCamera = Camera.main;
    }
  }

  void OnEnable()
  {
    myInput.Mouse.Enable();
    // 订阅所有相关事件
    myInput.Mouse.Click.started += OnClick;
    myInput.Mouse.Click.performed += OnClick;
    myInput.Mouse.Click.canceled += OnClick;
  }

  void OnDisable()
  {
    myInput.Mouse.Click.started -= OnClick;
    myInput.Mouse.Click.performed -= OnClick;
    myInput.Mouse.Click.canceled -= OnClick;
    myInput.Mouse.Disable();
  }

  void OnClick(InputAction.CallbackContext context)
  {
    if (context.interaction is PressInteraction press && press.behavior == PressBehavior.PressAndRelease)
    {
      if (context.started)
      {
        Debug.Log("开始按下");
      }
      else if (context.performed)
      {
        // PressAndRelease 会触发两次 performed：
        // 1. 按下时 (PerformedAndStayPerformed)
        // 2. 松开时 (Performed)
        if (!context.canceled) // 第一次 performed (按下)
        {
          Debug.Log("按下");
          HandlePress(context);
        }
        else // 第二次 performed (松开时，紧接着会有 canceled)
        {
          Debug.Log("松开 (performed)");
        }
      }
      else if (context.canceled)
      {
        Debug.Log("释放");
        HandleRelease(context);
      }
    }
  }

  private void HandlePress(InputAction.CallbackContext context)
  {
    Vector2 mousePosition = Mouse.current.position.ReadValue();
    Ray ray = myCamera.ScreenPointToRay(mousePosition);

    if (Physics.Raycast(ray, out RaycastHit hitInfo, Mathf.Infinity, ~ignoreLayer))
    {
      target = hitInfo.transform;
      if (target.TryGetComponent<Renderer>(out var renderer))
      {
        initialColor = renderer.material.color;
        renderer.material.color = Color.green;
        Debug.Log("物体变为绿色");
      }
    }
    else
    {
      Debug.Log("没有点击到物体");
      target = null;
    }
  }

  private void HandleRelease(InputAction.CallbackContext context)
  {
    if (target != null && target.TryGetComponent<Renderer>(out var renderer))
    {
      renderer.material.color = initialColor;
      Debug.Log("恢复物体颜色");
    }
  }
}
