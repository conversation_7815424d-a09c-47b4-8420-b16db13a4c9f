using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Interactions;

public class IPS07InputInteractions : MonoBehaviour
{
  // 用于使用Input System
  private PlayerInputs myInput;
  // 用于保存初始颜色，用于恢复
  private Color initialColor;
  // 用于保存鼠标点击的物体
  [SerializeField] private Transform target;
  // 用于添加相机组件
  public Camera myCamera;
  // 忽略地面层
  public LayerMask ignoreLayer;

  void Awake()
  {
    myInput ??= new PlayerInputs();
    if (myCamera == null)
    {
      myCamera = Camera.main;
    }
  }

  // 用于跟踪按下状态
  private bool isPressed = false;

  void OnEnable()
  {
    myInput.Mouse.Enable();
    // 需要同时监听 performed 和 canceled 事件
    myInput.Mouse.Click.performed += OnClickPerformed;
    myInput.Mouse.Click.canceled += OnClickCanceled;
  }

  void OnDisable()
  {
    myInput.Mouse.Click.performed -= OnClickPerformed;
    myInput.Mouse.Click.canceled -= OnClickCanceled;
    myInput.Mouse.Disable();
  }

  void OnClickPerformed(InputAction.CallbackContext context)
  {
    if (context.interaction is PressInteraction press && press.behavior == PressBehavior.PressAndRelease)
    {
      if (!isPressed)
      {
        // 第一次 performed = 按下
        isPressed = true;
        Debug.Log("按下");
      }
      else
      {
        // 第二次 performed = 松开时的 performed（紧接着会有 canceled）
        Debug.Log("松开时的 performed");
      }
    }
  }

  void OnClickCanceled(InputAction.CallbackContext context)
  {
    if (context.interaction is PressInteraction press && press.behavior == PressBehavior.PressAndRelease)
    {
      // canceled 事件 = 真正的释放
      isPressed = false;
      Debug.Log("释放");
    }
  }
}
