using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Interactions;

public class IPS07InputInteractions : MonoBehaviour
{
  // 用于使用Input System
  private PlayerInputs myInput;
  // 用于保存初始颜色，用于恢复
  private Color initialColor;
  // 用于保存鼠标点击的物体
  [SerializeField] private Transform target;
  // 用于添加相机组件
  public Camera myCamera;
  // 忽略地面层
  public LayerMask ignoreLayer;

  void Awake()
  {
    myInput ??= new PlayerInputs();
    if (myCamera == null)
    {
      myCamera = Camera.main;
    }
  }

  void OnEnable()
  {
    myInput.Mouse.Enable();
    // 单击事件
    myInput.Mouse.Click.performed += OnClickPerformed;
    myInput.Mouse.Click.canceled += OnClickCanceled;
  }

  void OnDisable()
  {
    myInput.Mouse.Click.performed -= OnClickPerformed;
    myInput.Mouse.Click.canceled -= OnClickCanceled;
    myInput.Mouse.Disable();
  }

  void OnClickPerformed(InputAction.CallbackContext context)
  {
    if (context.interaction is PressInteraction press)
    {
      //NOTE: 这里会执行方法中按下逻辑
      PressInteractionMethod(context, press);
    }
    if (context.interaction is HoldInteraction hold)
    {
      Debug.Log("持续按住");
    }
    if (context.interaction is TapInteraction tap)
    {
      Debug.Log("快速点击");
    }
  }

  void OnClickCanceled(InputAction.CallbackContext context)
  {
    //NOTE: 这里会执行方法中松开逻辑
    if (context.interaction is PressInteraction press)
    {
      PressInteractionMethod(context, press);
    }
  }


  //NOTE: 由于Press的Interaction选择的是PressAndRelease，因此需要Context的phase为Performed时，才会触发PressAndRelease的逻辑
  // 当按下时，将物体颜色变为绿色，松开时恢复初始颜色
  private void PressInteractionMethod(InputAction.CallbackContext ctx, PressInteraction press)
  {
    if (press.behavior == PressBehavior.PressOnly)
    {
      Debug.Log("只有按下时触发");
    }
    if (press.behavior == PressBehavior.ReleaseOnly)
    {
      Debug.Log("只有松开时触发");
    }
    // 按下时改变颜色，松开时恢复颜色
    if (press.behavior == PressBehavior.PressAndRelease)
    {
      if (ctx.performed)
      {
        // 点击时获取鼠标位置
        Vector2 mousePosition = Mouse.current.position.ReadValue();
        // 根据鼠标位置发射一条射线
        Ray ray = myCamera.ScreenPointToRay(mousePosition);
        // 获取射线碰撞到的物体，忽略地面层
        if (Physics.Raycast(ray, out RaycastHit hitInfo, Mathf.Infinity, ~ignoreLayer))
        {
          if (hitInfo.transform == null)
            Debug.Log("没有点击到物体");

          target = hitInfo.transform;
          initialColor = target.GetComponent<Renderer>().material.color;
        }
        target.GetComponent<Renderer>().material.color = Color.green;
      }
      if (ctx.canceled)
      {
        target.GetComponent<Renderer>().material.color = initialColor;
      }
    }
  }
}
