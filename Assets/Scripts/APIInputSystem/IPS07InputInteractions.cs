using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Interactions;

public class IPS07InputInteractions : MonoBehaviour
{
  // 用于使用Input System
  private PlayerInputs myInput;
  // 用于保存初始颜色，用于恢复
  private Color initialColor;
  // 用于保存鼠标点击的物体
  [SerializeField] private Transform target;
  // 用于添加相机组件
  public Camera myCamera;
  // 忽略地面层
  public LayerMask ignoreLayer;

  void Awake()
  {
    myInput ??= new PlayerInputs();
    if (myCamera == null)
    {
      myCamera = Camera.main;
    }
  }

  void OnEnable()
  {
    myInput.Mouse.Enable();
    // 单击事件
    myInput.Mouse.Click.performed += OnClickPerformed;
    myInput.Mouse.Click.canceled += OnClickCanceled;
  }

  void OnDisable()
  {
    myInput.Mouse.Click.performed -= OnClickPerformed;
    myInput.Mouse.Click.canceled -= OnClickCanceled;
    myInput.Mouse.Disable();
  }

  void OnClickPerformed(InputAction.CallbackContext context)
  {
    Debug.Log($"OnClickPerformed - Phase: {context.phase}, Started: {context.started}, Performed: {context.performed}, Canceled: {context.canceled}");

    if (context.interaction is PressInteraction press)
    {
      //NOTE: 这里会执行方法中按下逻辑
      PressInteractionMethod(context, press);
    }
    if (context.interaction is HoldInteraction hold)
    {
      Debug.Log("持续按住");
    }
    if (context.interaction is TapInteraction tap)
    {
      Debug.Log("快速点击");
    }
  }

  void OnClickCanceled(InputAction.CallbackContext context)
  {
    Debug.Log($"OnClickCanceled - Phase: {context.phase}, Started: {context.started}, Performed: {context.performed}, Canceled: {context.canceled}");

    //NOTE: 这里会执行方法中松开逻辑
    if (context.interaction is PressInteraction)
    {
      // 直接处理松开逻辑，不依赖于context.canceled状态
      if (target != null && target.TryGetComponent<Renderer>(out var renderer))
      {
        renderer.material.color = initialColor;
        Debug.Log("恢复物体颜色");
      }
    }
  }


  // 用于跟踪是否已经处理了按下事件
  private bool hasProcessedPress = false;

  //NOTE: 由于Press的Interaction选择的是PressAndRelease，因此需要Context的phase为Performed时，才会触发PressAndRelease的逻辑
  // 当按下时，将物体颜色变为绿色，松开时恢复初始颜色
  private void PressInteractionMethod(InputAction.CallbackContext ctx, PressInteraction press)
  {
    if (press.behavior == PressBehavior.PressAndRelease)
    {
      if (ctx.performed && !hasProcessedPress)
      {
        // 只在第一次performed时处理按下逻辑（真正的按下）
        hasProcessedPress = true;
        Vector2 mousePosition = Mouse.current.position.ReadValue();
        Ray ray = myCamera.ScreenPointToRay(mousePosition);

        if (Physics.Raycast(ray, out RaycastHit hitInfo, Mathf.Infinity, ~ignoreLayer))
        {
          target = hitInfo.transform;
          if (target.TryGetComponent<Renderer>(out var renderer))
          {
            initialColor = renderer.material.color;
            renderer.material.color = Color.green;
            Debug.Log("物体变为绿色");
          }
        }
        else
        {
          Debug.Log("没有点击到物体");
          target = null; // 重置 target
        }
      }

      if (ctx.canceled)
      {
        // 重置标志，准备下次按下
        hasProcessedPress = false;
        if (target != null && target.TryGetComponent<Renderer>(out var renderer))
        {
          renderer.material.color = initialColor;
          Debug.Log("恢复物体颜色");
        }
      }
    }
  }
}
