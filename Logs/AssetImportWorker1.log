Unity Editor version:    6000.2.0f1 (eed1c594c913)
Branch:                  6000.2/respin/6000.2.0f1-517f89d850d1
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.7 (Build 24G222)
Darwin version:          24.6.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        65536 MB
Date:                    2025-09-27T01:40:57Z
[Licensing::Module] Trying to connect to existing licensing client channel...
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cat" at "2025-09-27T01:40:57.869057Z"

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
/Users/<USER>/Documents/MyProject
-logFile
Logs/AssetImportWorker1.log
-srvPort
59813
-licensingIpc
LicenseClient-cat
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
-name AssetImport
Successfully changed project path to: /Users/<USER>/Documents/MyProject
/Users/<USER>/Documents/MyProject
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8413602112]  Target information:

Player connection [8413602112]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2859070665 [EditorId] 2859070665 [Version] 1048832 [Id] OSXEditor(0,mingyundemaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8413602112]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 2859070665 [EditorId] 2859070665 [Version] 1048832 [Id] OSXEditor(0,mingyundemaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8413602112] Host joined multi-casting on [***********:54997]...
Player connection [8413602112] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.09 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 2.51 ms.
Initialize engine version: 6000.2.0f1 (eed1c594c913)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Documents/MyProject/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M3 Max (high power)
Metal devices available: 1
0: Apple M3 Max (high power)
Using device Apple M3 Max (high power)
Initializing Metal device caps: Apple M3 Max
Mac GPU Family: 2
Apple GPU Family: 9
MSL Version: 3.0
Has Float MSAA: 1
Has Float Filtering: 1
ReadWrite Texture Tier: 2
[Licensing::Client] Code 10 while verifying Licensing Client signature (process Id: 21433, path: "/Applications/Unity Hub.app/Contents/Frameworks/UnityLicensingClient_V1.app/Contents/MacOS/Unity.Licensing.Client")
[Licensing::Module] LicensingClient has failed validation; ignoring
[Licensing::Client] Handshaking with LicensingClient:
  Version:                 1.17.3+a4e98f1
  Session Id:              3706c4b062e847c0a016064f9cedeb8e
  Correlation Id:          2b9d191e20f5680e1b781747c1085f77
  External correlation Id: 7648573846813774748
  Machine Id:              grnFDnHWQSWOHO69FTKqhp+4KtY=
[Licensing::Module] Successfully connected to LicensingClient on channel: "LicenseClient-cat" (connect: 0.00s, validation: 0.00s, handshake: 0.35s)
[Licensing::IpcConnector] Successfully connected to: "LicenseClient-cat-notifications" at "2025-09-27T01:40:58.218155Z"
[Licensing::Module] Licensing Background thread has ended after 0.35s
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56813
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
[Licensing::Module] License group:
  Id: 1375785438378-UnityPersXXXX
  Product: Unity Personal
  Type: Assigned
  Expiration: Unlimited
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
[Licensing::Client] Successfully resolved entitlement details
Register platform support module: /Applications/Unity/Hub/Editor/6000.2.0f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.002046 seconds.
- Loaded All Assemblies, in  0.202 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 75 ms
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.367 seconds
Domain Reload Profiling: 569ms
	BeginReloadAssembly (42ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (103ms)
		LoadAssemblies (42ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (101ms)
			TypeCache.Refresh (100ms)
				TypeCache.ScanAssembly (92ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (367ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (336ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (164ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (51ms)
			ProcessInitializeOnLoadAttributes (77ms)
			ProcessInitializeOnLoadMethodAttributes (40ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.547 seconds
Refreshing native plugins compatible for Editor in 0.48 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.560 seconds
Domain Reload Profiling: 1107ms
	BeginReloadAssembly (86ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (409ms)
		LoadAssemblies (178ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (261ms)
			TypeCache.Refresh (219ms)
				TypeCache.ScanAssembly (203ms)
			BuildScriptInfoCaches (32ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (560ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (453ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (142ms)
			ProcessInitializeOnLoadAttributes (204ms)
			ProcessInitializeOnLoadMethodAttributes (90ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Launching external process: /Applications/Unity/Hub/Editor/6000.2.0f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.51 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.41 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8663 unused Assets / (17.2 MB). Loaded Objects now: 9390.
Memory consumption went from 184.7 MB to 167.5 MB.
Total: 7.410500 ms (FindLiveObjects: 0.362917 ms CreateObjectMapping: 0.179250 ms MarkObjects: 4.655375 ms  DeleteObjects: 2.212417 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x350d97000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.512 seconds
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.617 seconds
Domain Reload Profiling: 1131ms
	BeginReloadAssembly (121ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (350ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (176ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (618ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (504ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (166ms)
			ProcessInitializeOnLoadAttributes (222ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.62 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (16.2 MB). Loaded Objects now: 9406.
Memory consumption went from 201.1 MB to 184.9 MB.
Total: 7.405916 ms (FindLiveObjects: 0.389542 ms CreateObjectMapping: 0.186583 ms MarkObjects: 4.805042 ms  DeleteObjects: 2.024333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 248237.827101 seconds.
  path: Assets/InputSystem/PlayerInputs.inputactions
  artifactKey: Guid(aa7d4a4d5e05d4a8294dc27b92e64992) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/InputSystem/PlayerInputs.inputactions using Guid(aa7d4a4d5e05d4a8294dc27b92e64992) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e91267b6254f840589a4488508492ca5') in 0.013645375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 4

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x350d97000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.489 seconds
Refreshing native plugins compatible for Editor in 0.46 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.602 seconds
Domain Reload Profiling: 1093ms
	BeginReloadAssembly (114ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (332ms)
		LoadAssemblies (179ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (163ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (602ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (502ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (165ms)
			ProcessInitializeOnLoadAttributes (216ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.59 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.54 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.7 MB). Loaded Objects now: 9412.
Memory consumption went from 201.6 MB to 186.0 MB.
Total: 8.030208 ms (FindLiveObjects: 0.457791 ms CreateObjectMapping: 0.186125 ms MarkObjects: 4.947750 ms  DeleteObjects: 2.438125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16d29f000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.501 seconds
Refreshing native plugins compatible for Editor in 0.52 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.598 seconds
Domain Reload Profiling: 1100ms
	BeginReloadAssembly (117ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (339ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (163ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (598ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (492ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (223ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.61 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.4 MB). Loaded Objects now: 9415.
Memory consumption went from 201.4 MB to 186.0 MB.
Total: 7.852291 ms (FindLiveObjects: 0.398458 ms CreateObjectMapping: 0.193875 ms MarkObjects: 4.932334 ms  DeleteObjects: 2.327125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.53 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.46 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8646 unused Assets / (13.9 MB). Loaded Objects now: 9415.
Memory consumption went from 189.5 MB to 175.6 MB.
Total: 7.888708 ms (FindLiveObjects: 0.411084 ms CreateObjectMapping: 0.172708 ms MarkObjects: 5.161125 ms  DeleteObjects: 2.142875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16d29f000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.501 seconds
Refreshing native plugins compatible for Editor in 0.58 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.601 seconds
Domain Reload Profiling: 1104ms
	BeginReloadAssembly (121ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (337ms)
		LoadAssemblies (185ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (180ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (162ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (602ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (505ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (165ms)
			ProcessInitializeOnLoadAttributes (225ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.53 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.9 MB). Loaded Objects now: 9418.
Memory consumption went from 201.5 MB to 185.5 MB.
Total: 7.696542 ms (FindLiveObjects: 0.417292 ms CreateObjectMapping: 0.196083 ms MarkObjects: 4.868125 ms  DeleteObjects: 2.214625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.499 seconds
Refreshing native plugins compatible for Editor in 0.50 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.588 seconds
Domain Reload Profiling: 1089ms
	BeginReloadAssembly (118ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (340ms)
		LoadAssemblies (180ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (588ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (486ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (159ms)
			ProcessInitializeOnLoadAttributes (212ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.58 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.55 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.8 MB). Loaded Objects now: 9421.
Memory consumption went from 201.6 MB to 185.8 MB.
Total: 7.322833 ms (FindLiveObjects: 0.404125 ms CreateObjectMapping: 0.191000 ms MarkObjects: 4.584917 ms  DeleteObjects: 2.142167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.497 seconds
Refreshing native plugins compatible for Editor in 0.47 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.587 seconds
Domain Reload Profiling: 1086ms
	BeginReloadAssembly (118ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (337ms)
		LoadAssemblies (179ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (183ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (587ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (162ms)
			ProcessInitializeOnLoadAttributes (214ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.39 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.6 MB). Loaded Objects now: 9424.
Memory consumption went from 201.7 MB to 186.1 MB.
Total: 8.097541 ms (FindLiveObjects: 0.382333 ms CreateObjectMapping: 0.184167 ms MarkObjects: 4.907083 ms  DeleteObjects: 2.623417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.502 seconds
Refreshing native plugins compatible for Editor in 0.49 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.603 seconds
Domain Reload Profiling: 1107ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (340ms)
		LoadAssemblies (180ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (604ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (505ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (223ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.63 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.6 MB). Loaded Objects now: 9427.
Memory consumption went from 201.7 MB to 186.1 MB.
Total: 7.957334 ms (FindLiveObjects: 0.407333 ms CreateObjectMapping: 0.175958 ms MarkObjects: 5.042459 ms  DeleteObjects: 2.331375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.53 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.40 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8646 unused Assets / (15.8 MB). Loaded Objects now: 9427.
Memory consumption went from 189.8 MB to 174.0 MB.
Total: 7.891041 ms (FindLiveObjects: 0.437750 ms CreateObjectMapping: 0.200750 ms MarkObjects: 4.913500 ms  DeleteObjects: 2.338250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.509 seconds
Refreshing native plugins compatible for Editor in 0.47 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.608 seconds
Domain Reload Profiling: 1120ms
	BeginReloadAssembly (124ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (343ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (608ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (504ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (217ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.72 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.52 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.9 MB). Loaded Objects now: 9430.
Memory consumption went from 201.7 MB to 185.8 MB.
Total: 7.874167 ms (FindLiveObjects: 0.400083 ms CreateObjectMapping: 0.183417 ms MarkObjects: 4.959791 ms  DeleteObjects: 2.330417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.500 seconds
Refreshing native plugins compatible for Editor in 0.47 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.610 seconds
Domain Reload Profiling: 1112ms
	BeginReloadAssembly (116ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (341ms)
		LoadAssemblies (180ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (610ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (507ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (169ms)
			ProcessInitializeOnLoadAttributes (219ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.59 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.38 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.5 MB). Loaded Objects now: 9433.
Memory consumption went from 201.8 MB to 186.2 MB.
Total: 7.585292 ms (FindLiveObjects: 0.388500 ms CreateObjectMapping: 0.174625 ms MarkObjects: 4.822875 ms  DeleteObjects: 2.198917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.505 seconds
Refreshing native plugins compatible for Editor in 0.47 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.590 seconds
Domain Reload Profiling: 1098ms
	BeginReloadAssembly (120ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (344ms)
		LoadAssemblies (184ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (591ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (162ms)
			ProcessInitializeOnLoadAttributes (211ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.53 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.8 MB). Loaded Objects now: 9436.
Memory consumption went from 201.8 MB to 185.9 MB.
Total: 7.964458 ms (FindLiveObjects: 0.470916 ms CreateObjectMapping: 0.207750 ms MarkObjects: 4.918542 ms  DeleteObjects: 2.367125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.499 seconds
Refreshing native plugins compatible for Editor in 0.46 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.601 seconds
Domain Reload Profiling: 1102ms
	BeginReloadAssembly (120ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (337ms)
		LoadAssemblies (182ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (601ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (496ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (216ms)
			ProcessInitializeOnLoadMethodAttributes (100ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.49 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.5 MB). Loaded Objects now: 9439.
Memory consumption went from 201.9 MB to 186.3 MB.
Total: 6.935250 ms (FindLiveObjects: 0.422750 ms CreateObjectMapping: 0.192791 ms MarkObjects: 4.277708 ms  DeleteObjects: 2.041625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.498 seconds
Refreshing native plugins compatible for Editor in 0.50 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.589 seconds
Domain Reload Profiling: 1089ms
	BeginReloadAssembly (123ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (333ms)
		LoadAssemblies (176ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (589ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (160ms)
			ProcessInitializeOnLoadAttributes (213ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.57 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.50 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.9 MB). Loaded Objects now: 9442.
Memory consumption went from 201.9 MB to 186.0 MB.
Total: 7.509709 ms (FindLiveObjects: 0.401541 ms CreateObjectMapping: 0.266000 ms MarkObjects: 4.669291 ms  DeleteObjects: 2.172459 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.508 seconds
Refreshing native plugins compatible for Editor in 0.48 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.597 seconds
Domain Reload Profiling: 1107ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (346ms)
		LoadAssemblies (188ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (597ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (217ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.54 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.55 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.7 MB). Loaded Objects now: 9445.
Memory consumption went from 202.0 MB to 186.2 MB.
Total: 7.798125 ms (FindLiveObjects: 0.418083 ms CreateObjectMapping: 0.202750 ms MarkObjects: 4.958959 ms  DeleteObjects: 2.217875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.498 seconds
Refreshing native plugins compatible for Editor in 0.45 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.587 seconds
Domain Reload Profiling: 1087ms
	BeginReloadAssembly (117ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (338ms)
		LoadAssemblies (181ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (182ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (587ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (486ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (210ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.45 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (16.1 MB). Loaded Objects now: 9448.
Memory consumption went from 202.0 MB to 185.9 MB.
Total: 7.568666 ms (FindLiveObjects: 0.460458 ms CreateObjectMapping: 0.203583 ms MarkObjects: 4.639417 ms  DeleteObjects: 2.264833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.502 seconds
Refreshing native plugins compatible for Editor in 0.41 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.607 seconds
Domain Reload Profiling: 1112ms
	BeginReloadAssembly (123ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (337ms)
		LoadAssemblies (181ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (164ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (608ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (502ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (221ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.53 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.49 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.6 MB). Loaded Objects now: 9451.
Memory consumption went from 201.9 MB to 186.4 MB.
Total: 8.255916 ms (FindLiveObjects: 0.387125 ms CreateObjectMapping: 0.176541 ms MarkObjects: 5.198209 ms  DeleteObjects: 2.493625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.45 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8646 unused Assets / (15.7 MB). Loaded Objects now: 9451.
Memory consumption went from 190.0 MB to 174.3 MB.
Total: 8.108250 ms (FindLiveObjects: 0.409625 ms CreateObjectMapping: 0.188750 ms MarkObjects: 5.362250 ms  DeleteObjects: 2.146542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.519 seconds
Refreshing native plugins compatible for Editor in 0.66 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.611 seconds
Domain Reload Profiling: 1132ms
	BeginReloadAssembly (126ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (351ms)
		LoadAssemblies (188ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (611ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (504ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (164ms)
			ProcessInitializeOnLoadAttributes (221ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.61 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.41 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (16.0 MB). Loaded Objects now: 9454.
Memory consumption went from 202.1 MB to 186.1 MB.
Total: 8.526917 ms (FindLiveObjects: 0.500667 ms CreateObjectMapping: 0.187541 ms MarkObjects: 5.253458 ms  DeleteObjects: 2.585000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.512 seconds
Refreshing native plugins compatible for Editor in 0.45 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.613 seconds
Domain Reload Profiling: 1128ms
	BeginReloadAssembly (125ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (346ms)
		LoadAssemblies (191ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (181ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (614ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (509ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (165ms)
			ProcessInitializeOnLoadAttributes (219ms)
			ProcessInitializeOnLoadMethodAttributes (105ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.69 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.50 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.5 MB). Loaded Objects now: 9457.
Memory consumption went from 202.1 MB to 186.6 MB.
Total: 8.147250 ms (FindLiveObjects: 0.463375 ms CreateObjectMapping: 0.244084 ms MarkObjects: 4.945833 ms  DeleteObjects: 2.493667 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d5e7000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.496 seconds
Refreshing native plugins compatible for Editor in 0.42 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.588 seconds
Domain Reload Profiling: 1087ms
	BeginReloadAssembly (118ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (336ms)
		LoadAssemblies (177ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (588ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (488ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (162ms)
			ProcessInitializeOnLoadAttributes (213ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.56 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.50 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (16.1 MB). Loaded Objects now: 9460.
Memory consumption went from 202.1 MB to 186.0 MB.
Total: 7.700625 ms (FindLiveObjects: 0.398250 ms CreateObjectMapping: 0.180084 ms MarkObjects: 4.778833 ms  DeleteObjects: 2.343250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16d987000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.503 seconds
Refreshing native plugins compatible for Editor in 0.50 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.592 seconds
Domain Reload Profiling: 1099ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (342ms)
		LoadAssemblies (181ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (593ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (496ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (160ms)
			ProcessInitializeOnLoadAttributes (218ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.59 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.7 MB). Loaded Objects now: 9463.
Memory consumption went from 202.2 MB to 186.4 MB.
Total: 7.979375 ms (FindLiveObjects: 0.411792 ms CreateObjectMapping: 0.209584 ms MarkObjects: 4.929625 ms  DeleteObjects: 2.428042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16d987000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.506 seconds
Refreshing native plugins compatible for Editor in 0.48 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.551 seconds
Domain Reload Profiling: 1060ms
	BeginReloadAssembly (123ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (341ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (552ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (452ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (146ms)
			ProcessInitializeOnLoadAttributes (197ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.53 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.48 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.7 MB). Loaded Objects now: 9466.
Memory consumption went from 202.1 MB to 186.4 MB.
Total: 7.463750 ms (FindLiveObjects: 0.414792 ms CreateObjectMapping: 0.202625 ms MarkObjects: 4.726500 ms  DeleteObjects: 2.119208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.506 seconds
Refreshing native plugins compatible for Editor in 0.58 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.595 seconds
Domain Reload Profiling: 1103ms
	BeginReloadAssembly (120ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (344ms)
		LoadAssemblies (184ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (595ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (498ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (160ms)
			ProcessInitializeOnLoadAttributes (220ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.58 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.53 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.3 MB). Loaded Objects now: 9469.
Memory consumption went from 202.2 MB to 186.9 MB.
Total: 7.912000 ms (FindLiveObjects: 0.412917 ms CreateObjectMapping: 0.215166 ms MarkObjects: 5.001125 ms  DeleteObjects: 2.282417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.515 seconds
Refreshing native plugins compatible for Editor in 0.48 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.584 seconds
Domain Reload Profiling: 1102ms
	BeginReloadAssembly (125ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (347ms)
		LoadAssemblies (185ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (189ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (171ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (585ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (484ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (210ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.56 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.50 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.5 MB). Loaded Objects now: 9472.
Memory consumption went from 202.3 MB to 186.8 MB.
Total: 6.792500 ms (FindLiveObjects: 0.425792 ms CreateObjectMapping: 0.249708 ms MarkObjects: 4.071333 ms  DeleteObjects: 2.045292 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.507 seconds
Refreshing native plugins compatible for Editor in 0.48 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.592 seconds
Domain Reload Profiling: 1101ms
	BeginReloadAssembly (124ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (341ms)
		LoadAssemblies (182ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (592ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (495ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (162ms)
			ProcessInitializeOnLoadAttributes (216ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.60 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.53 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.8 MB). Loaded Objects now: 9475.
Memory consumption went from 202.3 MB to 186.5 MB.
Total: 7.459292 ms (FindLiveObjects: 0.401042 ms CreateObjectMapping: 0.193041 ms MarkObjects: 4.773917 ms  DeleteObjects: 2.086041 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.516 seconds
Refreshing native plugins compatible for Editor in 0.46 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.591 seconds
Domain Reload Profiling: 1110ms
	BeginReloadAssembly (128ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (346ms)
		LoadAssemblies (189ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (183ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (166ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (591ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (495ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (221ms)
			ProcessInitializeOnLoadMethodAttributes (100ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.53 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.48 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.5 MB). Loaded Objects now: 9478.
Memory consumption went from 202.3 MB to 186.8 MB.
Total: 7.931291 ms (FindLiveObjects: 0.371458 ms CreateObjectMapping: 0.199167 ms MarkObjects: 5.107916 ms  DeleteObjects: 2.252458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.529 seconds
Refreshing native plugins compatible for Editor in 0.44 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.595 seconds
Domain Reload Profiling: 1127ms
	BeginReloadAssembly (125ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (58ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (362ms)
		LoadAssemblies (203ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (595ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (495ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (162ms)
			ProcessInitializeOnLoadAttributes (216ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.52 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.8 MB). Loaded Objects now: 9481.
Memory consumption went from 202.3 MB to 186.6 MB.
Total: 7.557417 ms (FindLiveObjects: 0.408666 ms CreateObjectMapping: 0.180250 ms MarkObjects: 4.712667 ms  DeleteObjects: 2.255375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.516 seconds
Refreshing native plugins compatible for Editor in 0.48 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.593 seconds
Domain Reload Profiling: 1112ms
	BeginReloadAssembly (121ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (353ms)
		LoadAssemblies (189ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (190ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (173ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (594ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (494ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (160ms)
			ProcessInitializeOnLoadAttributes (214ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.47 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.9 MB). Loaded Objects now: 9484.
Memory consumption went from 202.5 MB to 186.6 MB.
Total: 7.421667 ms (FindLiveObjects: 0.392500 ms CreateObjectMapping: 0.191125 ms MarkObjects: 4.714042 ms  DeleteObjects: 2.123500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.509 seconds
Refreshing native plugins compatible for Editor in 0.48 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.597 seconds
Domain Reload Profiling: 1108ms
	BeginReloadAssembly (125ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (342ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (597ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (496ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (167ms)
			ProcessInitializeOnLoadAttributes (215ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.98 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.49 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.8 MB). Loaded Objects now: 9487.
Memory consumption went from 202.5 MB to 186.7 MB.
Total: 7.406792 ms (FindLiveObjects: 0.444542 ms CreateObjectMapping: 0.184750 ms MarkObjects: 4.679833 ms  DeleteObjects: 2.097375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.536 seconds
Refreshing native plugins compatible for Editor in 0.41 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.605 seconds
Domain Reload Profiling: 1144ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (14ms)
	LoadAllAssembliesAndSetupDomain (361ms)
		LoadAssemblies (196ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (192ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (175ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (605ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (505ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (225ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.50 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.51 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.8 MB). Loaded Objects now: 9490.
Memory consumption went from 202.5 MB to 186.7 MB.
Total: 8.074458 ms (FindLiveObjects: 0.381000 ms CreateObjectMapping: 0.176250 ms MarkObjects: 5.229875 ms  DeleteObjects: 2.287042 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.515 seconds
Refreshing native plugins compatible for Editor in 0.49 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.599 seconds
Domain Reload Profiling: 1116ms
	BeginReloadAssembly (126ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (346ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (188ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (172ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (599ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (160ms)
			ProcessInitializeOnLoadAttributes (220ms)
			ProcessInitializeOnLoadMethodAttributes (103ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.55 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.44 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.4 MB). Loaded Objects now: 9493.
Memory consumption went from 202.5 MB to 187.2 MB.
Total: 8.182125 ms (FindLiveObjects: 0.440458 ms CreateObjectMapping: 0.188500 ms MarkObjects: 5.083833 ms  DeleteObjects: 2.468791 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.512 seconds
Refreshing native plugins compatible for Editor in 0.40 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.599 seconds
Domain Reload Profiling: 1113ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (340ms)
		LoadAssemblies (180ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (186ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (167ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (599ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (496ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (215ms)
			ProcessInitializeOnLoadMethodAttributes (100ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.63 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.53 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (16.0 MB). Loaded Objects now: 9496.
Memory consumption went from 202.5 MB to 186.5 MB.
Total: 7.805042 ms (FindLiveObjects: 0.411542 ms CreateObjectMapping: 0.191000 ms MarkObjects: 4.953583 ms  DeleteObjects: 2.248583 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.500 seconds
Refreshing native plugins compatible for Editor in 0.42 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.593 seconds
Domain Reload Profiling: 1096ms
	BeginReloadAssembly (120ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (338ms)
		LoadAssemblies (176ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (169ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (594ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (498ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (223ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.62 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.36 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.6 MB). Loaded Objects now: 9499.
Memory consumption went from 202.6 MB to 187.0 MB.
Total: 8.466708 ms (FindLiveObjects: 0.473542 ms CreateObjectMapping: 0.205667 ms MarkObjects: 5.199333 ms  DeleteObjects: 2.587875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.507 seconds
Refreshing native plugins compatible for Editor in 0.46 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.592 seconds
Domain Reload Profiling: 1101ms
	BeginReloadAssembly (124ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (341ms)
		LoadAssemblies (182ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (592ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (491ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (161ms)
			ProcessInitializeOnLoadAttributes (216ms)
			ProcessInitializeOnLoadMethodAttributes (96ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.56 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.45 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.9 MB). Loaded Objects now: 9502.
Memory consumption went from 202.7 MB to 186.8 MB.
Total: 7.710125 ms (FindLiveObjects: 0.464791 ms CreateObjectMapping: 0.245000 ms MarkObjects: 4.751709 ms  DeleteObjects: 2.248417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.517 seconds
Refreshing native plugins compatible for Editor in 0.50 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.610 seconds
Domain Reload Profiling: 1129ms
	BeginReloadAssembly (125ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (348ms)
		LoadAssemblies (189ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (184ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (610ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (508ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (168ms)
			ProcessInitializeOnLoadAttributes (218ms)
			ProcessInitializeOnLoadMethodAttributes (104ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.59 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.53 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.4 MB). Loaded Objects now: 9505.
Memory consumption went from 202.7 MB to 187.3 MB.
Total: 8.011833 ms (FindLiveObjects: 0.411250 ms CreateObjectMapping: 0.194584 ms MarkObjects: 4.987708 ms  DeleteObjects: 2.417833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
[Licensing::Client] Successfully resolved entitlement details
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.501 seconds
Refreshing native plugins compatible for Editor in 0.46 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.599 seconds
Domain Reload Profiling: 1103ms
	BeginReloadAssembly (120ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (339ms)
		LoadAssemblies (180ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (183ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (165ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (600ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (499ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (162ms)
			ProcessInitializeOnLoadAttributes (219ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.59 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.58 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.9 MB). Loaded Objects now: 9508.
Memory consumption went from 202.7 MB to 186.8 MB.
Total: 8.047458 ms (FindLiveObjects: 0.412125 ms CreateObjectMapping: 0.201875 ms MarkObjects: 5.118375 ms  DeleteObjects: 2.314708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x16da87000 may have been prematurely finalized
Duplicate assembly 'System.Runtime.CompilerServices.Unsafe.dll' with different versions detected, using 'Packages/com.unity.collections/Unity.Collections.Tests/System.Runtime.CompilerServices.Unsafe/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a' and ignoring 'Packages/com.unity.ai.assistant/Plugins/Markdig/Dependencies/System.Runtime.CompilerServices.Unsafe.dll, AssemblyName=System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a'.Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.505 seconds
Refreshing native plugins compatible for Editor in 0.49 ms, found 3 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.595 seconds
Domain Reload Profiling: 1102ms
	BeginReloadAssembly (120ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (13ms)
	LoadAllAssembliesAndSetupDomain (343ms)
		LoadAssemblies (183ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (185ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (168ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (595ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (496ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (221ms)
			ProcessInitializeOnLoadMethodAttributes (101ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
WARNING: Shader Unsupported: 'Universal Render Pipeline/VR/SpatialMapping/Wireframe' - All subshaders removed
WARNING: Shader Did you use #pragma only_renderers and omit this platform?
WARNING: Shader If subshaders removal was intentional, you may have forgotten turning Fallback off?
Refreshing native plugins compatible for Editor in 0.53 ms, found 3 plugins.
Preloading 1 native plugins for Editor in 1.50 ms.
Unloading 54 Unused Serialized files (Serialized files now loaded: 0)
Unloading 8661 unused Assets / (15.0 MB). Loaded Objects now: 9511.
Memory consumption went from 202.7 MB to 187.7 MB.
Total: 7.742250 ms (FindLiveObjects: 0.400875 ms CreateObjectMapping: 0.205167 ms MarkObjects: 4.840958 ms  DeleteObjects: 2.294958 ms)

Prepare: number of updated asset objects reloaded= 0
